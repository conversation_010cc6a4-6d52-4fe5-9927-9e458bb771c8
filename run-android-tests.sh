#!/bin/bash

# Script to run Android tests with proper environment setup
echo "🚀 Starting Android Tests"
echo "========================"

# Set Android environment variables for Windows SDK
export ANDROID_HOME="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk"
export ANDROID_SDK_ROOT="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk"
export PATH="$PATH:/mnt/c/Users/<USER>/AppData/Local/Android/Sdk/platform-tools"

echo "✅ Environment variables set:"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "ANDROID_SDK_ROOT: $ANDROID_SDK_ROOT"

# Check if ADB is available
if ! command -v "/mnt/c/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/adb.exe" &> /dev/null; then
    echo "❌ ADB not found. Please install Android Studio on Windows."
    exit 1
fi

echo "✅ ADB found"

# Check for connected devices
echo ""
echo "📱 Checking for connected devices..."
DEVICES=$("/mnt/c/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/adb.exe" devices | grep -v "List of devices attached" | grep -v "^$")

if [ -z "$DEVICES" ]; then
    echo "❌ No Android devices found!"
    echo ""
    echo "Please:"
    echo "1. Open Android Studio on Windows"
    echo "2. Go to Tools → AVD Manager"
    echo "3. Start an emulator"
    echo "4. Or connect a physical device with USB debugging enabled"
    echo ""
    echo "Then run this script again."
    exit 1
fi

echo "✅ Found devices:"
echo "$DEVICES"

# Create a symlink for adb to make it easier to use
if [ ! -f "/usr/local/bin/adb" ]; then
    echo ""
    echo "🔗 Creating ADB symlink..."
    sudo ln -sf "/mnt/c/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/adb.exe" /usr/local/bin/adb
    echo "✅ ADB symlink created"
fi

echo ""
echo "🧪 Running Android app tests..."
npm run android.app
