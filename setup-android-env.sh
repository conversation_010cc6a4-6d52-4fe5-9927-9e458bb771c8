#!/bin/bash

# Setup script for Android testing in WSL2
# This script helps configure environment variables for Windows Android SDK

echo "🔧 Android Environment Setup for WSL2"
echo "======================================"

# Get Windows username
echo "Please enter your Windows username:"
read -p "Username: " WINDOWS_USERNAME

if [ -z "$WINDOWS_USERNAME" ]; then
    echo "❌ Username cannot be empty"
    exit 1
fi

# Set paths
ANDROID_HOME="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk"
ANDROID_SDK_ROOT="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk"
PLATFORM_TOOLS="$ANDROID_HOME/platform-tools"

echo ""
echo "📁 Checking Android SDK installation..."

# Check if Android SDK exists
if [ ! -d "$ANDROID_HOME" ]; then
    echo "❌ Android SDK not found at: $ANDROID_HOME"
    echo ""
    echo "Please install Android Studio on Windows first:"
    echo "1. Download from: https://developer.android.com/studio"
    echo "2. Install on Windows (not WSL2)"
    echo "3. Complete the setup wizard"
    echo "4. Run this script again"
    exit 1
fi

echo "✅ Android SDK found at: $ANDROID_HOME"

# Check if platform-tools exist
if [ ! -f "$PLATFORM_TOOLS/adb.exe" ]; then
    echo "❌ ADB not found at: $PLATFORM_TOOLS/adb.exe"
    echo "Please ensure Android SDK is properly installed"
    exit 1
fi

echo "✅ ADB found at: $PLATFORM_TOOLS/adb.exe"

# Create environment setup script
cat > android-env.sh << EOF
#!/bin/bash
# Android environment variables for WSL2
export ANDROID_HOME="$ANDROID_HOME"
export ANDROID_SDK_ROOT="$ANDROID_SDK_ROOT"
export PATH="\$PATH:$PLATFORM_TOOLS"

echo "✅ Android environment variables set:"
echo "ANDROID_HOME: \$ANDROID_HOME"
echo "ANDROID_SDK_ROOT: \$ANDROID_SDK_ROOT"
echo "PATH includes: $PLATFORM_TOOLS"
EOF

chmod +x android-env.sh

echo ""
echo "✅ Environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Start Android emulator in Windows Android Studio"
echo "2. Run: source ./android-env.sh"
echo "3. Run: adb devices (should show your emulator)"
echo "4. Run: npm run android.app"
echo ""
echo "💡 To make this permanent, add these lines to your ~/.bashrc:"
echo "export ANDROID_HOME=\"$ANDROID_HOME\""
echo "export ANDROID_SDK_ROOT=\"$ANDROID_SDK_ROOT\""
echo "export PATH=\"\$PATH:$PLATFORM_TOOLS\""
