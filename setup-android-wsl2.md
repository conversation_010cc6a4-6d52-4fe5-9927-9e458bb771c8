# Android Testing Setup for WSL2

## Problem
Android emulators don't work well in WSL2 due to virtualization limitations.

## Solution: Use Windows Android Studio + WSL2

### Step 1: Install Android Studio on Windows
1. Download Android Studio from https://developer.android.com/studio
2. Install it on Windows (not in WSL2)
3. Open Android Studio and complete the setup wizard
4. Install Android SDK and create a virtual device (AVD)

### Step 2: Start Android Emulator in Windows
1. Open Android Studio on Windows
2. Go to Tools → AVD Manager
3. Create a new virtual device (e.g., Pixel 6, API 33/34)
4. Start the emulator

### Step 3: Connect WSL2 to Windows Emulator

In your WSL2 terminal, run these commands:

```bash
# Set Android SDK path to Windows installation
export ANDROID_HOME="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk"
export ANDROID_SDK_ROOT="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk"
export PATH="$PATH:/mnt/c/Users/<USER>/AppData/Local/Android/Sdk/platform-tools"

# Kill any existing ADB server
adb kill-server

# Start ADB server and connect to Windows emulator
adb start-server
adb devices
```

### Step 4: Update Configuration
Update the device name in `config/wdio.android.app.conf.ts` to match your emulator:

```typescript
"appium:deviceName": "YOUR_EMULATOR_NAME",
"appium:platformVersion": "14.0", // or your Android version
```

### Step 5: Run Tests
```bash
# Set environment variables and run tests
ANDROID_HOME="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk" \
ANDROID_SDK_ROOT="/mnt/c/Users/<USER>/AppData/Local/Android/Sdk" \
PATH="$PATH:/mnt/c/Users/<USER>/AppData/Local/Android/Sdk/platform-tools" \
npm run android.app
```

## Alternative: Physical Android Device

1. Enable Developer Options on your Android device
2. Enable USB Debugging
3. Connect via USB
4. Run `adb devices` to verify connection
5. Update device name in config to match your device

## Troubleshooting

### If ADB doesn't find devices:
```bash
# Check Windows ADB is running
/mnt/c/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/adb.exe devices

# Connect to Windows ADB from WSL2
adb connect 127.0.0.1:5555
```

### If emulator is slow:
- Enable hardware acceleration in Windows
- Allocate more RAM to the emulator
- Use x86_64 system images instead of ARM

### Current Status:
✅ Android SDK environment variables: Fixed
✅ App file: Downloaded
✅ Import paths: Fixed in config files
✅ Device configuration: Ready to update
❌ Emulator: Needs Windows Android Studio setup
