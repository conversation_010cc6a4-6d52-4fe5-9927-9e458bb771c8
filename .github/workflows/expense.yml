name: Expense Contribution

on:
  workflow_dispatch:
    inputs:
      prNumber:
        description: "Number of the PR (without #)"
        required: true
      amount:
        description: "The expense amount you like to grant for the contribution in $"
        required: true
        type: choice
        options:
          - 15
          - 25
          - 35
          - 50
          - 100
          - 150
          - 200
          - 250
          - 300
          - 350
          - 400
          - 450
          - 500
          - 550
          - 600
          - 650
          - 700
          - 750
          - 800
          - 850
          - 900
          - 950
          - 1000

jobs:
  authorize:
    runs-on: ubuntu-latest
    steps:
      - uses: octokit/request-action@v2.3.1
        with:
          route: GET /orgs/:organisation/teams/:team/memberships/${{ github.actor }}
          team: technical-steering-committee
          organisation: webdriverio
        env:
          GITHUB_TOKEN: ${{ secrets.WDIO_BOT_GITHUB_TOKEN }}
  expense:
    permissions:
      contents: write
      id-token: write
    needs: [authorize]
    runs-on: ubuntu-latest
    steps:
      - name: Run Expense Flow
        uses: webdriverio/expense-action@v1
        with:
          prNumber: ${{ github.event.inputs.prNumber }}
          amount: ${{ github.event.inputs.amount }}
        env:
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
          GH_TOKEN: ${{ secrets.WDIO_BOT_GITHUB_TOKEN }}
