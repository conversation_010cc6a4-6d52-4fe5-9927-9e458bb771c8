{"name": "appium-boilerplate", "version": "5.3.1", "author": "Wim <PERSON> <<EMAIL>>", "license": "MIT", "description": "Boilerplate project to run WebdriverIO tests with Appium to test native applications on iOS and Android", "homepage": "https://github.com/webdriverio/appium-boilerplate#readme", "repository": {"type": "git", "url": "git+https://github.com/webdriverio/appium-boilerplate.git"}, "bugs": {"url": "https://github.com/webdriverio/appium-boilerplate/issues"}, "engines": {"node": "^16.13.0 || >=18.0.0"}, "keywords": ["WebdriverIO", "WebDriver", "appium", "native app", "hybrid", "android", "ios"], "type": "module", "scripts": {"android.browserstack.app": "wdio run ./config/browserstack/wdio.android.bs.app.conf.ts", "android.sauce.emulators.app.eu": "REGION=eu  wdio config/saucelabs/wdio.android.emulators.app.conf.ts", "android.sauce.emulators.app.us": "wdio run config/saucelabs/wdio.android.emulators.app.conf.ts", "android.lt.browser.emulator": "wdio run config/lambdatest/wdio.android.emulator.browser.conf.ts", "android.lt.browser.realdevice": "wdio run config/lambdatest/wdio.android.realdevice.browser.conf.ts", "android.lt.app.realdevice": "wdio run config/lambdatest/wdio.android.realdevice.app.conf.ts", "android.sauce.rdc.app.eu": "REGION=eu wdio config/saucelabs/wdio.android.rdc.app.conf.ts", "android.sauce.rdc.app.us": "wdio run config/saucelabs/wdio.android.rdc.app.conf.ts", "android.testingbot.app": "wdio ./config/testingbot/wdio.android.tb.app.conf.ts", "android.app": "wdio run config/wdio.android.app.conf.ts", "android.app.cucumber": "wdio run config/wdio.android.app.cucumber.conf.ts", "android.browser": "wdio run config/wdio.android.browser.conf.ts", "browser": "wdio run config/wdio.browser.conf.ts", "ios.app": "wdio run config/wdio.ios.app.conf.ts", "ios.sauce.simulators.app.eu": "REGION=eu wdio ./config/saucelabs/wdio.ios.simulators.app.conf.ts", "ios.sauce.simulators.app.us": "wdio run ./config/saucelabs/wdio.ios.simulators.app.conf.ts", "ios.browser": "wdio run config/wdio.ios.browser.conf.ts", "ios.lt.browser.simulator": "wdio run config/lambdatest/wdio.ios.simulator.browser.conf.ts", "ios.lt.browser.realdevice": "wdio run config/lambdatest/wdio.ios.realdevice.browser.conf.ts", "lint": "eslint config tests"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "@wdio/appium-service": "^9.7.1", "@wdio/browserstack-service": "^9.7.1", "@wdio/cli": "^9.7.1", "@wdio/cucumber-framework": "^9.6.4", "@wdio/globals": "^9.7.1", "@wdio/local-runner": "^9.7.1", "@wdio/mocha-framework": "^9.6.4", "@wdio/sauce-service": "^9.7.1", "@wdio/spec-reporter": "^9.6.3", "@wdio/testingbot-service": "^9.7.1", "appium": "^2.15.0", "appium-uiautomator2-driver": "^3.10.0", "appium-xcuitest-driver": "8.1.0", "eslint-plugin-wdio": "^9.6.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "wdio-lambdatest-service": "^4.0.0"}}